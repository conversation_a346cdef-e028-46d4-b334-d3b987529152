<template>
  <div
    class="ai-chat-container"
    v-motion
    :initial="{ y: 50, opacity: 0 }"
    :enter="{ y: 0, opacity: 1, transition: { duration: 500 } }"
  >
    <!-- 调试区域 -->
    <DebugPanel
      v-model:course-id="debugConfig.courseId"
      v-model:material-id="debugConfig.materialId"
    />

    <!-- 聊天记录区域 -->
    <ChatHistory
      :messages="messages"
      :is-typing="isTyping"
      @clear-conversation="handleClearConversation"
    />

    <!-- 输入区域 -->
    <ChatInput v-model:mode="chatMode" :is-sending="isSending" @send-message="handleSendMessage" />

    <!-- 确认弹窗 -->
    <ConfirmDialog
      v-model:visible="showConfirmDialog"
      title="清除对话"
      message="确定要清除所有对话记录吗？此操作无法撤销。"
      @confirm="confirmClearConversation"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { chatAPI } from '../../utils/api.js'
import DebugPanel from './DebugPanel.vue'
import ChatHistory from './ChatHistory.vue'
import ChatInput from './ChatInput.vue'
import ConfirmDialog from './ConfirmDialog.vue'

// 状态管理
const conversationId = ref(null)
const messages = ref([])
const isTyping = ref(false)
const isSending = ref(false)
const showConfirmDialog = ref(false)
const chatMode = ref('condense_plus_context')

const debugConfig = reactive({
  courseId: '',
  materialId: '',
})

const systemConfig = ref(null)

// 初始化
onMounted(async () => {
  conversationId.value = uuidv4()
  await loadSystemConfig()

  // 添加一些示例消息来展示界面效果
  messages.value = [
    {
      id: uuidv4(),
      type: 'user',
      content: '你好，请介绍一下这个知识图谱系统。',
      timestamp: new Date(Date.now() - 60000),
    },
    {
      id: uuidv4(),
      type: 'ai',
      content:
        '您好！这是一个基于Vue3和D3.js构建的知识图谱可视化系统。它可以帮助您：\n\n1. 可视化复杂的知识关系\n2. 交互式探索数据节点\n3. 智能问答和信息检索\n\n您可以通过拖拽、缩放等方式与图谱进行交互。有什么具体问题我可以为您解答吗？',
      timestamp: new Date(Date.now() - 50000),
      sources: [
        { title: '系统文档', name: '产品介绍' },
        { title: '技术架构', name: 'Vue3技术栈' },
      ],
    },
  ]
})

// 加载系统配置
async function loadSystemConfig() {
  try {
    systemConfig.value = await chatAPI.getConfig()
  } catch (error) {
    console.error('Failed to load system config:', error)
    // 设置默认配置，避免阻塞UI
    systemConfig.value = {
      maxTokens: 4000,
      availableEngines: ['condense_plus_context', 'simple'],
    }
  }
}

// 发送消息
async function handleSendMessage(question) {
  if (!question.trim() || isSending.value) return

  isSending.value = true

  // 添加用户消息
  const userMessage = {
    id: uuidv4(),
    type: 'user',
    content: question,
    timestamp: new Date(),
  }
  messages.value.push(userMessage)

  // 显示打字指示器
  isTyping.value = true

  try {
    const requestData = {
      conversation_id: conversationId.value,
      question: question,
      chat_engine_type: chatMode.value,
      course_id: debugConfig.courseId || undefined,
      course_material_id: debugConfig.materialId || undefined,
    }

    const result = await chatAPI.sendMessage(requestData)

    // 添加AI回复
    const aiMessage = {
      id: uuidv4(),
      type: 'ai',
      content: result.answer,
      timestamp: new Date(),
      sources: result.sources || [],
    }
    messages.value.push(aiMessage)
  } catch (error) {
    console.error('Failed to send message:', error)

    // 添加错误消息
    const errorMessage = {
      id: uuidv4(),
      type: 'ai',
      content: '抱歉，发生了错误，请稍后重试。',
      timestamp: new Date(),
      isError: true,
    }
    messages.value.push(errorMessage)
  } finally {
    isTyping.value = false
    isSending.value = false
  }
}

// 清除对话
function handleClearConversation() {
  showConfirmDialog.value = true
}

async function confirmClearConversation() {
  try {
    await chatAPI.clearConversation(conversationId.value)
    messages.value = []
    conversationId.value = uuidv4()
  } catch (error) {
    console.error('Failed to clear conversation:', error)
  }
}
</script>

<style scoped>
.ai-chat-container {
  width: 45%;
  min-width: 320px;
  height: 100vh;
  background: white;
  border-radius: 8px;
  box-shadow:
    0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  border: 1px solid oklch(0.92 0.01 220);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

@media (max-width: 768px) {
  .ai-chat-container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
    margin: 0;
  }
}
</style>
