# AI聊天组件

这是一个基于Vue 3构建的独立AI聊天组件，与知识图谱系统完全分离。

## 技术栈

- **Vue 3.5** - 前端框架
- **Tailwind CSS v4** - 样式框架
- **@heroicons/vue** - 图标库
- **@vueuse/motion** - 动画库
- **@vueuse/core** - 工具库
- **date-fns** - 日期处理
- **uuid** - 唯一标识符生成

## 功能特性

### 🎯 核心功能
- ✅ 实时聊天对话
- ✅ 消息历史记录
- ✅ 打字指示器
- ✅ 消息来源显示
- ✅ 对话清除功能

### 🎨 界面设计
- ✅ 响应式布局（占屏幕右侧45%宽度）
- ✅ 用户消息（右侧蓝色）
- ✅ AI消息（左侧灰色）
- ✅ 头像显示
- ✅ 时间戳
- ✅ 动画效果

### 🔧 调试功能
- ✅ 可折叠调试面板
- ✅ Course ID 配置
- ✅ Material ID 配置
- ✅ 聊天模式切换

### 🚀 聊天模式
- **根据课件** (`condense_plus_context`) - 基于课程材料的问答
- **通用知识** (`simple`) - 通用知识问答

## 项目结构

```
src/
├── components/chat/
│   ├── AIChatComponent.vue      # 主聊天组件
│   ├── DebugPanel.vue          # 调试面板
│   ├── ChatHistory.vue         # 聊天历史
│   ├── ChatMessage.vue         # 单条消息
│   ├── TypingIndicator.vue     # 打字指示器
│   ├── ChatInput.vue           # 输入组件
│   └── ConfirmDialog.vue       # 确认对话框
├── views/
│   └── ChatView.vue            # 聊天页面
├── utils/
│   └── api.js                  # API配置
└── router/
    └── index.js                # 路由配置
```

## 环境配置

### 环境变量 (.env)
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8000

# 开发环境配置
VITE_APP_TITLE=知识图谱聊天系统
VITE_APP_ENV=development
```

### 后端API接口

组件需要以下API接口：

1. **获取系统配置**
   - `GET /api/v1/conversation/config`

2. **发送聊天消息**
   - `POST /api/v1/conversation/chat`
   - 请求体：
     ```json
     {
       "conversation_id": "uuid",
       "question": "用户问题",
       "chat_engine_type": "condense_plus_context",
       "course_id": "可选",
       "course_material_id": "可选"
     }
     ```

3. **清除对话**
   - `DELETE /api/v1/conversation/conversations/{conversation_id}`

## 使用方法

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 访问聊天页面
打开浏览器访问：`http://localhost:3000/chat`

### 3. 使用聊天功能
1. 在输入框中输入问题
2. 选择聊天模式（根据课件/通用知识）
3. 点击发送或按Enter键
4. 查看AI回复和参考来源
5. 使用调试面板配置Course ID和Material ID（可选）

## 开发说明

### 添加新功能
1. 在对应的组件文件中添加功能
2. 更新API配置（如需要）
3. 测试功能是否正常工作

### 样式定制
- 使用Tailwind CSS类进行样式定制
- 主要颜色配置在各组件的style部分
- 支持响应式设计

### API集成
- 所有API调用都通过 `src/utils/api.js` 统一管理
- 支持错误处理和重试机制
- 可以轻松切换不同的后端环境

## 注意事项

1. **独立性**：此聊天组件与知识图谱模块完全独立
2. **路由**：默认路由设置为 `/chat`
3. **响应式**：在移动设备上会占据100%宽度
4. **错误处理**：包含完整的错误处理和用户反馈
5. **性能**：使用Vue 3的Composition API优化性能

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查 `.env` 文件中的 `VITE_API_BASE_URL` 配置
   - 确保后端服务正在运行

2. **样式问题**
   - 确保Tailwind CSS正确配置
   - 检查 `vite.config.js` 中的Tailwind插件

3. **动画不工作**
   - 确保 `@vueuse/motion` 插件已正确注册
   - 检查 `main.js` 中的插件配置

## 更新日志

- **v1.0.0** - 初始版本，包含基础聊天功能
- 支持实时对话、消息历史、调试面板等核心功能
